<x-sidebar-app>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Time Tracking</h2>
                        <div class="flex space-x-3">
                            <a href="{{ route('time-tracking.create') }}" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>Manual Entry
                            </a>
                        </div>
                    </div>

                    <!-- Active Timer Section -->
                    @if($activeTimer)
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-3"></div>
                                    <div>
                                        <h3 class="font-medium text-green-800">Timer Running</h3>
                                        <p class="text-sm text-green-600">
                                            {{ $activeTimer->project->name }} 
                                            @if($activeTimer->task)
                                                - {{ $activeTimer->task->title }}
                                            @endif
                                        </p>
                                        <p class="text-xs text-green-500">
                                            Started: {{ $activeTimer->start_time->format('g:i A') }}
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="text-lg font-mono text-green-800" id="timer-display">
                                        {{ $activeTimer->getElapsedTimeAttribute() }}
                                    </div>
                                    <button onclick="stopTimer()" 
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-stop mr-2"></i>Stop
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Quick Timer Start -->
                    @if(!$activeTimer && $projects->count() > 0)
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <h3 class="font-medium text-blue-800 mb-3">Quick Start Timer</h3>
                            <form id="quick-timer-form" class="flex items-end space-x-3">
                                @csrf
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Project</label>
                                    <select name="project_id" required class="w-full border-gray-300 rounded-md shadow-sm">
                                        <option value="">Select Project</option>
                                        @foreach($projects as $project)
                                            <option value="{{ $project->id }}">{{ $project->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                    <input type="text" name="description" required 
                                           class="w-full border-gray-300 rounded-md shadow-sm"
                                           placeholder="What are you working on?">
                                </div>
                                <button type="submit" 
                                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-play mr-2"></i>Start Timer
                                </button>
                            </form>
                        </div>
                    @endif

                    <!-- Time Entries List -->
                    @if($timeEntries->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date & Time
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Project
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Duration
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Billable
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($timeEntries as $entry)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div>{{ $entry->start_time->format('M j, Y') }}</div>
                                                <div class="text-xs text-gray-500">
                                                    {{ $entry->start_time->format('g:i A') }}
                                                    @if($entry->end_time)
                                                        - {{ $entry->end_time->format('g:i A') }}
                                                    @endif
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $entry->project->name }}
                                                </div>
                                                @if($entry->task)
                                                    <div class="text-xs text-gray-500">
                                                        {{ $entry->task->title }}
                                                    </div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                {{ $entry->description }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $entry->getFormattedDurationAttribute() }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($entry->is_billable)
                                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                        Billable
                                                    </span>
                                                @else
                                                    <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                        Non-billable
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('time-tracking.edit', $entry) }}" 
                                                       class="text-blue-600 hover:text-blue-900">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="{{ route('time-tracking.destroy', $entry) }}" 
                                                          class="inline" onsubmit="return confirm('Are you sure?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6">
                            {{ $timeEntries->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <i class="fas fa-clock text-3xl text-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No time entries yet</h3>
                            <p class="text-gray-500 mb-6">Start tracking your time to see entries here.</p>
                            @if($projects->count() > 0)
                                <a href="{{ route('time-tracking.create') }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Add Time Entry
                                </a>
                            @else
                                <p class="text-sm text-gray-400">Create a project first to start tracking time.</p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Timer functionality
        function stopTimer() {
            fetch('{{ route("time-tracking.stop") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        }

        // Quick timer form
        document.getElementById('quick-timer-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('{{ route("time-tracking.start") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        });
    </script>
    @endpush
</x-sidebar-app>
