<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Client;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $projects = Auth::user()->getAllInvolvedProjects()
            ->with(['client', 'clientUser', 'tasks', 'timeEntries'])
            ->latest()
            ->paginate(10);

        return view('projects.index', compact('projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $clients = Client::where('user_id', Auth::id())->get();
        $clientUsers = User::clients()->where('id', '!=', Auth::id())->get();

        return view('projects.create', compact('clients', 'clientUsers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'client_type' => 'required|in:traditional,user',
            'client_id' => 'required_if:client_type,traditional|exists:clients,id',
            'client_user_id' => 'required_if:client_type,user|exists:users,id',
            'budget' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'due_date' => 'nullable|date|after:today',
            'status' => 'required|in:planning,active,on_hold,completed,cancelled',
        ]);

        $project = new Project($validated);
        $project->user_id = Auth::id();

        // Set the appropriate client field based on type
        if ($validated['client_type'] === 'traditional') {
            $project->client_id = $validated['client_id'];
            $project->client_user_id = null;
        } else {
            $project->client_user_id = $validated['client_user_id'];
            $project->client_id = null;
        }

        $project->save();

        return redirect()->route('projects.index')
            ->with('success', 'Project created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $project)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        $project->load(['client', 'clientUser', 'tasks.assignedUser', 'timeEntries.user', 'projectMembers.user']);

        return view('projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project)
    {
        // Check if user can edit this project (only owner can edit)
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this project.');
        }

        $clients = Client::where('user_id', Auth::id())->get();
        $clientUsers = User::clients()->where('id', '!=', Auth::id())->get();

        return view('projects.edit', compact('project', 'clients', 'clientUsers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $project)
    {
        // Check if user can edit this project
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this project.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'client_type' => 'required|in:traditional,user',
            'client_id' => 'required_if:client_type,traditional|exists:clients,id',
            'client_user_id' => 'required_if:client_type,user|exists:users,id',
            'budget' => 'nullable|numeric|min:0',
            'hourly_rate' => 'nullable|numeric|min:0',
            'due_date' => 'nullable|date',
            'status' => 'required|in:planning,active,on_hold,completed,cancelled',
        ]);

        // Set the appropriate client field based on type
        if ($validated['client_type'] === 'traditional') {
            $project->client_id = $validated['client_id'];
            $project->client_user_id = null;
        } else {
            $project->client_user_id = $validated['client_user_id'];
            $project->client_id = null;
        }

        $project->update($validated);

        return redirect()->route('projects.show', $project)
            ->with('success', 'Project updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project)
    {
        // Check if user can delete this project
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to delete this project.');
        }

        $project->delete();

        return redirect()->route('projects.index')
            ->with('success', 'Project deleted successfully.');
    }

    /**
     * Show project dashboard.
     */
    public function dashboard(Project $project)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        $project->load(['client', 'clientUser', 'tasks.assignedUser', 'timeEntries.user', 'projectMembers.user']);

        return view('projects.dashboard', compact('project'));
    }

    /**
     * Update project status.
     */
    public function updateStatus(Request $request, Project $project)
    {
        // Check if user can edit this project
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this project.');
        }

        $validated = $request->validate([
            'status' => 'required|in:planning,active,on_hold,completed,cancelled',
        ]);

        $project->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Project status updated successfully.',
            'project' => $project
        ]);
    }

    /**
     * Add member to project.
     */
    public function addMember(Request $request, Project $project)
    {
        // Check if user can edit this project
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this project.');
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|in:member,manager',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        // Check if user is already a member
        if ($project->projectMembers()->where('user_id', $validated['user_id'])->exists()) {
            return response()->json(['error' => 'User is already a member of this project.'], 422);
        }

        $project->projectMembers()->create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Member added successfully.'
        ]);
    }

    /**
     * Remove member from project.
     */
    public function removeMember(Project $project, $memberId)
    {
        // Check if user can edit this project
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this project.');
        }

        $member = $project->projectMembers()->findOrFail($memberId);
        $member->delete();

        return response()->json([
            'success' => true,
            'message' => 'Member removed successfully.'
        ]);
    }

    /**
     * Update project member.
     */
    public function updateMember(Request $request, Project $project, $memberId)
    {
        // Check if user can edit this project
        if ($project->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this project.');
        }

        $validated = $request->validate([
            'role' => 'required|in:member,manager',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        $member = $project->projectMembers()->findOrFail($memberId);
        $member->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Member updated successfully.',
            'member' => $member->load('user')
        ]);
    }
}
