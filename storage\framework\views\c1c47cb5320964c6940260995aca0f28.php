<?php if (isset($component)) { $__componentOriginalb74e8d81074516492afd394bc835e266 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb74e8d81074516492afd394bc835e266 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidebar-app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar-app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Projects</h2>
                        <a href="<?php echo e(route('projects.create')); ?>" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-plus mr-2"></i>New Project
                        </a>
                    </div>

                    <?php if($projects->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                                    <div class="p-6">
                                        <div class="flex justify-between items-start mb-4">
                                            <h3 class="text-lg font-semibold text-gray-800 truncate">
                                                <?php echo e($project->name); ?>

                                            </h3>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                                <?php if($project->status === 'active'): ?> bg-green-100 text-green-800
                                                <?php elseif($project->status === 'planning'): ?> bg-blue-100 text-blue-800
                                                <?php elseif($project->status === 'on_hold'): ?> bg-yellow-100 text-yellow-800
                                                <?php elseif($project->status === 'completed'): ?> bg-gray-100 text-gray-800
                                                <?php else: ?> bg-red-100 text-red-800
                                                <?php endif; ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $project->status))); ?>

                                            </span>
                                        </div>

                                        <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                            <?php echo e($project->description ?: 'No description provided.'); ?>

                                        </p>

                                        <div class="space-y-2 text-sm text-gray-500">
                                            <div class="flex items-center">
                                                <i class="fas fa-user w-4 h-4 mr-2"></i>
                                                <span><?php echo e($project->getClientNameAttribute()); ?></span>
                                            </div>
                                            
                                            <?php if($project->due_date): ?>
                                                <div class="flex items-center">
                                                    <i class="fas fa-calendar w-4 h-4 mr-2"></i>
                                                    <span>Due: <?php echo e($project->due_date->format('M j, Y')); ?></span>
                                                </div>
                                            <?php endif; ?>

                                            <div class="flex items-center">
                                                <i class="fas fa-tasks w-4 h-4 mr-2"></i>
                                                <span><?php echo e($project->tasks->count()); ?> tasks</span>
                                            </div>

                                            <div class="flex items-center">
                                                <i class="fas fa-clock w-4 h-4 mr-2"></i>
                                                <span><?php echo e($project->getTotalHoursAttribute()); ?>h logged</span>
                                            </div>
                                        </div>

                                        <div class="mt-4 pt-4 border-t border-gray-100">
                                            <div class="flex justify-between items-center">
                                                <a href="<?php echo e(route('projects.show', $project)); ?>" 
                                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                    View Details
                                                </a>
                                                
                                                <?php if($project->user_id === auth()->id()): ?>
                                                    <div class="flex space-x-2">
                                                        <a href="<?php echo e(route('projects.edit', $project)); ?>" 
                                                           class="text-gray-600 hover:text-gray-800">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <div class="mt-6">
                            <?php echo e($projects->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <i class="fas fa-project-diagram text-3xl text-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
                            <p class="text-gray-500 mb-6">Get started by creating your first project.</p>
                            <a href="<?php echo e(route('projects.create')); ?>" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>Create Project
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb74e8d81074516492afd394bc835e266)): ?>
<?php $attributes = $__attributesOriginalb74e8d81074516492afd394bc835e266; ?>
<?php unset($__attributesOriginalb74e8d81074516492afd394bc835e266); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb74e8d81074516492afd394bc835e266)): ?>
<?php $component = $__componentOriginalb74e8d81074516492afd394bc835e266; ?>
<?php unset($__componentOriginalb74e8d81074516492afd394bc835e266); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/projects/index.blade.php ENDPATH**/ ?>