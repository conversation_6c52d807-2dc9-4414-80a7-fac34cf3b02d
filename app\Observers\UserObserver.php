<?php

namespace App\Observers;

use App\Models\User;
use App\Models\Plan;
use App\Models\UserSubscription;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // Skip auto-assignment for admin users
        if ($user->hasRole('admin')) {
            return;
        }

        // Auto-assign free plan to new users
        $freePlan = Plan::getFreePlan();

        if ($freePlan) {
            // Create free subscription
            UserSubscription::create([
                'user_id' => $user->id,
                'plan_id' => $freePlan->id,
                'status' => 'active',
                'starts_at' => now(),
                'amount_paid' => 0,
                'currency' => config('services.currency.code', 'USD'),
            ]);

            // Update user's current plan
            $user->update(['current_plan_id' => $freePlan->id]);
        } else {
            // Log warning if free plan is not available
            \Illuminate\Support\Facades\Log::warning('Free plan not found during user registration', [
                'user_id' => $user->id,
                'user_email' => $user->email
            ]);
        }
    }
    /**
     * Handle the User "deleting" event.
     */
    public function deleting(User $user): void
    {
        // Delete all related data in proper order
        $user->followUps()->delete();
        $user->tdsRecords()->delete();

        // Delete invoices and their items
        foreach ($user->invoices as $invoice) {
            $invoice->items()->delete();
            $invoice->delete();
        }

        // Delete contracts
        $user->contracts()->delete();

        // Delete clients
        $user->clients()->delete();

        // Delete user files and their physical files
        foreach ($user->files as $file) {
            // Delete physical file from storage
            \Illuminate\Support\Facades\Storage::disk($file->disk)->delete($file->path);
            $file->delete();
        }

        // Handle projects where user is the client
        // Set client_user_id to null for projects where this user was the client
        $user->clientProjects()->update(['client_user_id' => null]);

        // Delete projects owned by the user and their related data
        foreach ($user->projects as $project) {
            // Delete time entries
            $project->timeEntries()->delete();

            // Delete tasks
            $project->tasks()->delete();

            // Delete project members
            $project->projectMembers()->delete();

            // Delete the project
            $project->delete();
        }

        // Remove user from project memberships
        $user->projectMemberships()->delete();

        // Delete time entries created by user
        $user->timeEntries()->delete();

        // Delete tasks created by user
        $user->tasks()->delete();
    }
}
